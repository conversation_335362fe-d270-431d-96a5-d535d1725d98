/**
 * Event Manager
 * Handles all event listeners and delegation for the popup
 */
import { BaseManager } from '../core/BaseManager.js';

export class EventManager extends BaseManager {
    constructor(controller) {
        super(controller);
        this.eventListeners = new Map(); // Track event listeners for cleanup
    }

    async init() {
        await super.init();
        this.setupEventListeners();
        this.setupMessageListeners();
        this.setupEventDelegation();
    }

    /**
     * Add event listener with tracking for cleanup
     */
    addEventListenerTracked(element, event, handler, options = {}) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        
        if (!element) {
            console.warn(`Element not found for event listener: ${element}`);
            return;
        }

        element.addEventListener(event, handler, options);
        
        // Track for cleanup
        const key = `${element.id || 'unknown'}-${event}`;
        this.eventListeners.set(key, { element, event, handler, options });
    }

    /**
     * Setup all main event listeners
     */
    setupEventListeners() {
        // API Key Management
        this.addEventListenerTracked('saveApiKey', 'click', async () => {
            const apiKey = document.getElementById('apiKeyInput').value.trim();
            if (apiKey) {
                await this.controller.settingsManager.saveApiKey(apiKey);
                this.controller.uiManager.showSuccess('API Key saved successfully!');
                
                // Navigate to actions section after successful save
                setTimeout(() => {
                    this.controller.navigateToSection('actionsSection');
                }, 1000); // Small delay to let user see the success message
            } else {
                this.controller.uiManager.showError('Please enter a valid API key');
            }
        });

        // Quick Actions
        this.addEventListenerTracked('analyzeSelection', 'click', () => {
            this.controller.analysisManager.analyzeSelection();
        });

        this.addEventListenerTracked('analyzePage', 'click', () => {
            this.controller.analysisManager.analyzePage();
        });

        this.addEventListenerTracked('customAnalysis', 'click', async () => {
            // Check pro status and navigate accordingly
            await this.controller.settingsManager.handleCustomAnalysisClick();
        });

        this.addEventListenerTracked('viewAnalysisHistory', 'click', () => {
            this.controller.navigateToSection('analysisHistorySection');
            this.controller.dataManager.loadAndDisplayAnalysis();
        });

        this.addEventListenerTracked('managePrompts', 'click', async () => {
            await this.controller.settingsManager.handlePromptManagerClick();
        });

        this.addEventListenerTracked('scrapeAndAnalyze', 'click', async () => {
            await this.controller.settingsManager.handleScrapeAndAnalyzeClick();
        });

        // Custom Analysis Form
        this.addEventListenerTracked('backToActions', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('runCustomAnalysis', 'click', () => {
            this.controller.analysisManager.runCustomAnalysis();
        });

        this.addEventListenerTracked('loadSelectedPrompt', 'click', () => {
            this.controller.promptUIManager.loadSelectedPrompt();
        });

        this.addEventListenerTracked('saveCurrentPrompt', 'click', () => {
            this.controller.promptUIManager.saveCurrentPrompt();
        });

        // Scrape Form Events
        this.addEventListenerTracked('backToActionsFromScrape', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('analyzeScrapeUrl', 'click', () => {
            this.controller.analysisManager.analyzeScrapeUrl();
        });

        // Results Section
        this.addEventListenerTracked('backToActionsFromResults', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('copyResults', 'click', () => {
            this.controller.analysisManager.copyResults();
        });

        this.addEventListenerTracked('exportResults', 'click', () => {
            this.controller.analysisManager.exportResults();
        });

        // Navigation buttons
        this.addEventListenerTracked('backToActionsFromHistory', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('backToActionsFromHelp', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('backToActionsFromAbout', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('backToActionsFromSettings', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('backToActionsFromUpgrade', 'click', () => {
            this.controller.goBack();
        });

        this.addEventListenerTracked('backToActionsFromPrompts', 'click', () => {
            this.controller.goBack();
        });

        // Prompt Management Section
        this.addEventListenerTracked('promptSearch', 'input', (e) => {
            this.controller.promptUIManager.searchPrompts(e.target.value);
        });

        this.addEventListenerTracked('promptSort', 'change', (e) => {
            this.controller.promptUIManager.sortPrompts(e.target.value);
        });

        this.addEventListenerTracked('addNewPrompt', 'click', async () => {
            await this.controller.promptUIManager.openPromptEditor();
        });

        this.addEventListenerTracked('exportPrompts', 'click', () => {
            this.controller.promptUIManager.exportPrompts();
        });

        this.addEventListenerTracked('importPrompts', 'click', () => {
            document.getElementById('importPromptsFile').click();
        });

        this.addEventListenerTracked('importPromptsFile', 'change', (e) => {
            this.controller.promptUIManager.importPrompts(e.target.files[0]);
        });

        // Prompt Editor Modal
        this.addEventListenerTracked('closePromptEditor', 'click', () => {
            this.controller.uiManager.closePromptEditor();
        });

        this.addEventListenerTracked('cancelPromptEdit', 'click', () => {
            this.controller.uiManager.closePromptEditor();
        });

        this.addEventListenerTracked('savePromptEdit', 'click', () => {
            this.controller.promptUIManager.savePromptEdit();
        });

        // Close modal when clicking outside
        this.addEventListenerTracked('promptEditorModal', 'click', (e) => {
            if (e.target.id === 'promptEditorModal') {
                this.controller.uiManager.closePromptEditor();
            }
        });

        // Key Management Modal Events
        this.addEventListenerTracked('closeKeyManagement', 'click', () => {
            this.controller.uiManager.closeKeyManagementModal();
        });

        // Close key management modal when clicking outside
        this.addEventListenerTracked('keyManagementModal', 'click', (e) => {
            if (e.target.id === 'keyManagementModal') {
                this.controller.uiManager.closeKeyManagementModal();
            }
        });

        this.addEventListenerTracked('validateProKey', 'click', async () => {
            await this.controller.settingsManager.handleProKeyValidation();
        });

        // Clear pro cache button
        this.addEventListenerTracked('clearProCache', 'click', async () => {
            await this.controller.settingsManager.handleClearProCache();
        });

        // Allow Enter key to validate pro key
        this.addEventListenerTracked('proKeyInput', 'keypress', async (e) => {
            if (e.key === 'Enter') {
                await this.controller.settingsManager.handleProKeyValidation();
            }
        });

        // Data source radio button change handler
        document.addEventListener('change', (e) => {
            if (e.target.name === 'dataSource') {
                const urlInputGroup = document.getElementById('urlInputGroup');
                if (urlInputGroup) {
                    urlInputGroup.style.display = e.target.value === 'url' ? 'block' : 'none';
                }
            }
        });

        // Footer Links
        this.addEventListenerTracked('settingsLink', 'click', (e) => {
            e.preventDefault();
            this.controller.navigateToSection('settingsSection');
        });

        this.addEventListenerTracked('helpLink', 'click', (e) => {
            e.preventDefault();
            this.controller.navigateToSection('helpSection');
        });

        this.addEventListenerTracked('aboutLink', 'click', (e) => {
            e.preventDefault();
            this.controller.navigateToSection('aboutSection');
        });

        // Analysis History List
        this.addEventListenerTracked('analysisHistoryList', 'click', (e) => {
            // Check if delete button was clicked
            const deleteBtn = e.target.closest('.delete-btn');
            if (deleteBtn) {
                e.stopPropagation(); // Prevent card click
                const analysisId = deleteBtn.dataset.analysisId;
                this.controller.dataManager.deleteAnalysis(analysisId);
                return;
            }
            
            // Handle card click for viewing analysis
            const historyItem = e.target.closest('.history-item');
            if (historyItem && historyItem.dataset.analysisId) {
                this.controller.dataManager.viewAnalysisFromHistory(historyItem.dataset.analysisId);
            }
        });
    }

    /**
     * Setup message listeners for communication with content script
     */
    setupMessageListeners() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'triggerAnalysis') {
                console.log('Received triggerAnalysis message:', request.type, request.data);
                
                if (request.type === 'selection' && request.data) {
                    this.controller.analysisManager.analyzeSelectionWithText(request.data);
                } else if (request.type === 'page' && request.data) {
                    this.controller.analysisManager.analyzePageWithData(request.data);
                }
                
                sendResponse({ success: true, message: 'Analysis triggered' });
                return true; // Indicates an asynchronous response
            }
        });
    }

    /**
     * Setup event delegation for dynamically created elements
     */
    setupEventDelegation() {
        // Event delegation for history items
        document.addEventListener('click', (e) => {
            console.log('🔍 Click event detected:', e.target);

            const deleteBtn = e.target.closest('.delete-btn');
            const telegramBtn = e.target.closest('.telegram-send-btn');
            const discordBtn = e.target.closest('.discord-send-btn');
            const viewDetailsLink = e.target.closest('.view-details-link');
            const historyItem = e.target.closest('.history-item');

            // Handle delete button
            if (deleteBtn) {
                e.stopPropagation();
                const analysisId = deleteBtn.dataset.analysisId;
                console.log('🗑️ Delete button clicked for analysis:', analysisId);
                if (analysisId) {
                    this.controller.dataManager.deleteAnalysis(analysisId);
                }
                return;
            }

            // Handle Telegram button
            if (telegramBtn) {
                e.stopPropagation();
                const analysisId = telegramBtn.dataset.analysisId;
                console.log('📱 Telegram button clicked for analysis:', analysisId);
                if (analysisId) {
                    this.controller.integrationManager.sendAnalysisToTelegram(analysisId);
                }
                return;
            }

            // Handle Discord button
            if (discordBtn) {
                e.stopPropagation();
                const analysisId = discordBtn.dataset.analysisId;
                console.log('💬 Discord button clicked for analysis:', analysisId);
                if (analysisId) {
                    this.controller.integrationManager.sendAnalysisToDiscord(analysisId);
                }
                return;
            }

            // Handle view details link
            if (viewDetailsLink) {
                e.stopPropagation();
                const analysisId = viewDetailsLink.dataset.analysisId;
                console.log('👁️ View details clicked for analysis:', analysisId);
                if (analysisId) {
                    this.controller.dataManager.viewAnalysisFromHistory(analysisId);
                }
                return;
            }

            // Handle history item click (only if no other button was clicked)
            if (historyItem && !deleteBtn && !telegramBtn && !discordBtn && !viewDetailsLink) {
                const analysisId = historyItem.dataset.analysisId;
                console.log('📄 History item clicked for analysis:', analysisId);
                if (analysisId) {
                    this.controller.dataManager.viewAnalysisFromHistory(analysisId);
                }
            }
        });

        // Event delegation for prompt card buttons
        document.addEventListener('click', (e) => {
            const button = e.target.closest('.btn-icon[data-action]');
            if (button) {
                e.stopPropagation();
                const action = button.dataset.action;
                const promptId = button.dataset.promptId;

                if (promptId) {
                    switch (action) {
                        case 'use':
                            this.controller.promptUIManager.usePrompt(promptId);
                            break;
                        case 'copy':
                            this.controller.promptUIManager.copyPromptContent(promptId);
                            break;
                        case 'pin':
                            this.controller.promptUIManager.togglePromptPin(promptId);
                            break;
                        case 'edit':
                            this.controller.promptUIManager.editPrompt(promptId);
                            break;
                        case 'delete':
                            this.controller.promptUIManager.deletePromptConfirm(promptId);
                            break;
                    }
                }
            }
        });

        // Event delegation for tag clicks
        document.addEventListener('click', (e) => {
            const tag = e.target.closest('.tag[data-tag]');
            if (tag) {
                e.stopPropagation();
                const tagName = tag.dataset.tag;
                this.controller.promptUIManager.filterByTag(tagName);
            }
        });

        // Event delegation for tag filter buttons
        document.addEventListener('click', (e) => {
            const filterButton = e.target.closest('.tag-filter[data-filter-tag]');
            if (filterButton) {
                e.stopPropagation();
                const tagName = filterButton.dataset.filterTag || null;
                this.controller.promptUIManager.filterByTag(tagName);
            }
        });

        // Enhanced keyboard navigation for prompt cards and tags
        document.addEventListener('keydown', (e) => {
            const tag = e.target.closest('.tag');

            if (tag && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                if (tag.dataset.tag) {
                    this.controller.promptUIManager.filterByTag(tag.dataset.tag);
                }
            }
        });

        // Event delegation for pagination controls
        document.addEventListener('click', (e) => {
            const paginationBtn = e.target.closest('.pagination-btn[data-page]');
            if (paginationBtn && !paginationBtn.disabled) {
                e.stopPropagation();
                const page = parseInt(paginationBtn.dataset.page);
                const paginationContainer = paginationBtn.closest('.pagination-wrapper');

                if (paginationContainer) {
                    // Determine which pagination this belongs to
                    if (paginationContainer.id === 'historyPagination') {
                        this.controller.historyPagination.goToPage(page);
                    } else if (paginationContainer.id === 'promptPagination') {
                        this.controller.promptPagination.goToPage(page);
                    }
                }
            }
        });
    }

    /**
     * Cleanup all tracked event listeners
     */
    cleanup() {
        for (const [key, { element, event, handler, options }] of this.eventListeners) {
            try {
                element.removeEventListener(event, handler, options);
            } catch (error) {
                console.warn(`Failed to remove event listener ${key}:`, error);
            }
        }
        this.eventListeners.clear();
        super.cleanup();
    }
}
