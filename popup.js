// Agent Hustle Pro Analyzer - Popup Script (Refactored)
import { API_ENDPOINT, PAGINATION_CONFIG } from './config.js';
import { checkProStatus, setProKey, getMaskedProKey, getDetailedProStatus, getMembershipTimeRemaining, removeProKey, refreshProStatus } from './js/user/proStatus.js';
import { runDailyMembershipCheck } from './js/utils/membershipChecker.js';
import { promptManager } from './js/user/promptManager.js';
import { PaginationManager } from './js/utils/pagination.js';
import {
    saveTelegramSettings,
    getTelegramSettings,
    clearTelegramSettings,
    isTelegramConfigured,
    getMaskedBotToken,
    saveTelegramAutoSendSettings,
    getTelegramAutoSendSettings,
    updateTelegramAutoSendFailureCount,
    updateTelegramAutoSendSuccess
} from './js/user/telegramSettings.js';
import {
    sendAnalysisToTelegram,
    testTelegramConnection
} from './js/integrations/telegram.js';
import {
    saveDiscordSettings,
    getDiscordSettings,
    clearDiscordSettings,
    isDiscordConfigured,
    getMaskedWebhookUrl,
    saveDiscordAutoSendSettings,
    getDiscordAutoSendSettings,
    updateDiscordAutoSendFailureCount,
    updateDiscordAutoSendSuccess
} from './js/user/discordSettings.js';
import {
    sendAnalysisToDiscord,
    testDiscordWebhook
} from './js/integrations/discord.js';

// Import new modular architecture
import { PopupController } from './js/popup/core/PopupController.js';
import { UIManager } from './js/popup/ui/UIManager.js';
import { EventManager } from './js/popup/ui/EventManager.js';
import { AnalysisManager } from './js/popup/analysis/AnalysisManager.js';
import { DataManager } from './js/popup/data/DataManager.js';
import { SettingsManager } from './js/popup/settings/SettingsManager.js';
import { PromptUIManager } from './js/popup/prompts/PromptUIManager.js';
import { AutoSendManager } from './js/popup/integrations/AutoSendManager.js';
import { IntegrationManager } from './js/popup/integrations/IntegrationManager.js';

class AgentHustleAnalyzer {
    constructor() {
        // Create the new modular controller
        this.controller = new PopupController();
        
        // Set up core properties for backward compatibility
        this.controller.baseUrl = API_ENDPOINT;
        this.controller.apiKey = null;
        this.controller.sessionId = `session-${Date.now()}`;
        this.controller.currentAnalysis = null;
        this.controller.currentEditingPrompt = null;
        this.controller.currentFilterTag = null;
        
        // Initialize pagination managers
        this.controller.historyPagination = new PaginationManager({
            itemsPerPage: PAGINATION_CONFIG.HISTORY_ITEMS_PER_PAGE,
            containerId: 'historyPagination',
            onPageChange: () => this.controller.dataManager.loadAndDisplayAnalysis()
        });
        
        this.controller.promptPagination = new PaginationManager({
            itemsPerPage: PAGINATION_CONFIG.PROMPTS_ITEMS_PER_PAGE,
            containerId: 'promptPagination',
            onPageChange: () => this.controller.promptUIManager.refreshPromptList()
        });
        
        this.init();
    }

    async init() {
        try {
            // Register all managers
            this.controller.registerManager('dataManager', new DataManager(this.controller));
            this.controller.registerManager('uiManager', new UIManager(this.controller));
            this.controller.registerManager('settingsManager', new SettingsManager(this.controller));
            this.controller.registerManager('analysisManager', new AnalysisManager(this.controller));
            this.controller.registerManager('promptUIManager', new PromptUIManager(this.controller));
            this.controller.registerManager('integrationManager', new IntegrationManager(this.controller));
            this.controller.registerManager('autoSendManager', new AutoSendManager(this.controller));
            this.controller.registerManager('eventManager', new EventManager(this.controller));
            
            // Initialize the controller (which initializes all managers)
            await this.controller.init();
            
            // Update UI after initialization
            this.controller.uiManager.updateUI();
            
            // Set initial section context AFTER API key is loaded
            this.controller.currentSection = this.controller.apiKey ? 'actionsSection' : 'apiKeySection';
            
            // Show the correct initial section
            this.controller.uiManager.showSection(this.controller.currentSection);
            
            console.log('AgentHustleAnalyzer initialized with modular architecture');
        } catch (error) {
            console.error('Failed to initialize AgentHustleAnalyzer:', error);
            // Fallback error display
            document.body.innerHTML = `<div style="padding: 20px; color: red;">Failed to initialize: ${error.message}</div>`;
        }
    }

    // Backward compatibility methods - delegate to appropriate managers
    get baseUrl() { return this.controller.baseUrl; }
    get apiKey() { return this.controller.apiKey; }
    get sessionId() { return this.controller.sessionId; }
    get currentAnalysis() { return this.controller.currentAnalysis; }
    get currentEditingPrompt() { return this.controller.currentEditingPrompt; }
    get currentFilterTag() { return this.controller.currentFilterTag; }
    get historyPagination() { return this.controller.historyPagination; }
    get promptPagination() { return this.controller.promptPagination; }

    set currentAnalysis(value) { this.controller.currentAnalysis = value; }
    set currentEditingPrompt(value) { this.controller.currentEditingPrompt = value; }
    set currentFilterTag(value) { this.controller.currentFilterTag = value; }

    // Delegate methods to managers for backward compatibility
    showSection(sectionId) { return this.controller.uiManager.showSection(sectionId); }
    showError(message) { return this.controller.uiManager.showError(message); }
    showSuccess(message) { return this.controller.uiManager.showSuccess(message); }
    showMembershipWarning(warning) { return this.controller.uiManager.showMembershipWarning(warning); }
    updateUI() { return this.controller.uiManager.updateUI(); }
    updateApiStatus() { return this.controller.uiManager.updateApiStatus(); }
    updateProKeyStatus(status, message) { return this.controller.uiManager.updateProKeyStatus(status, message); }
    
    // Analysis methods
    analyzeSelection() { return this.controller.analysisManager.analyzeSelection(); }
    analyzePage() { return this.controller.analysisManager.analyzePage(); }
    runCustomAnalysis() { return this.controller.analysisManager.runCustomAnalysis(); }
    analyzeScrapeUrl() { return this.controller.analysisManager.analyzeScrapeUrl(); }
    performAnalysis(prompt, type) { return this.controller.analysisManager.performAnalysis(prompt, type); }
    displayResults(result, type, date) { return this.controller.analysisManager.displayResults(result, type, date); }
    copyResults() { return this.controller.analysisManager.copyResults(); }
    exportResults() { return this.controller.analysisManager.exportResults(); }
    analyzeSelectionWithText(text) { return this.controller.analysisManager.analyzeSelectionWithText(text); }
    analyzePageWithData(data) { return this.controller.analysisManager.analyzePageWithData(data); }
    formatAnalysisContent(content, summary) { return this.controller.analysisManager.formatAnalysisContent(content, summary); }
    formatToolCalls(toolCalls) { return this.controller.analysisManager.formatToolCalls(toolCalls); }
    convertToMarkdown(data) { return this.controller.analysisManager.convertToMarkdown(data); }
    
    // Data methods
    saveAnalysis(type, result) { return this.controller.dataManager.saveAnalysis(type, result); }
    loadAndDisplayAnalysis() { return this.controller.dataManager.loadAndDisplayAnalysis(); }
    deleteAnalysis(id) { return this.controller.dataManager.deleteAnalysis(id); }
    viewAnalysisFromHistory(id) { return this.controller.dataManager.viewAnalysisFromHistory(id); }
    
    // Settings methods
    handleCustomAnalysisClick() { return this.controller.settingsManager.handleCustomAnalysisClick(); }
    handlePromptManagerClick() { return this.controller.settingsManager.handlePromptManagerClick(); }
    handleProKeyValidation() { return this.controller.settingsManager.handleProKeyValidation(); }
    loadSettingsContent() { return this.controller.settingsManager.loadSettingsContent(); }
    
    // Prompt methods
    loadSavedPromptsSelect() { return this.controller.promptUIManager.loadSavedPromptsSelect(); }
    loadSelectedPrompt() { return this.controller.promptUIManager.loadSelectedPrompt(); }
    saveCurrentPrompt() { return this.controller.promptUIManager.saveCurrentPrompt(); }
    loadPromptManagement() { return this.controller.promptUIManager.loadPromptManagement(); }
    refreshPromptList() { return this.controller.promptUIManager.refreshPromptList(); }
    searchPrompts(query) { return this.controller.promptUIManager.searchPrompts(query); }
    sortPrompts(sortBy) { return this.controller.promptUIManager.sortPrompts(sortBy); }
    openPromptEditor(prompt) { return this.controller.promptUIManager.openPromptEditor(prompt); }
    savePromptEdit() { return this.controller.promptUIManager.savePromptEdit(); }
    exportPrompts() { return this.controller.promptUIManager.exportPrompts(); }
    importPrompts(file) { return this.controller.promptUIManager.importPrompts(file); }
    
    // Integration methods
    sendAnalysisToTelegram(id) { return this.controller.integrationManager.sendAnalysisToTelegram(id); }
    sendAnalysisToDiscord(id) { return this.controller.integrationManager.sendAnalysisToDiscord(id); }

    // Auto-send methods
    handleAutoSend(type, result) { return this.controller.autoSendManager.handleAutoSend(type, result); }
    handleTelegramAutoSend(data) { return this.controller.autoSendManager.handleTelegramAutoSend(data); }
    handleDiscordAutoSend(data) { return this.controller.autoSendManager.handleDiscordAutoSend(data); }

    // Settings integration methods
    loadApiKey() { return this.controller.settingsManager.loadApiKey(); }
    saveApiKey(key) { return this.controller.settingsManager.saveApiKey(key); }
    loadMembershipInfo() { return this.controller.settingsManager.loadMembershipInfo(); }
    loadTelegramSettings() { return this.controller.settingsManager.loadTelegramSettings(); }
    loadDiscordSettings() { return this.controller.settingsManager.loadDiscordSettings(); }
    loadFirecrawlSettings() { return this.controller.settingsManager.loadFirecrawlSettings(); }
    saveTelegramSettings() { return this.controller.settingsManager.saveTelegramSettings(); }
    saveDiscordSettings() { return this.controller.settingsManager.saveDiscordSettings(); }
    saveFirecrawlSettings() { return this.controller.settingsManager.saveFirecrawlSettings(); }
    testTelegramConnection() { return this.controller.settingsManager.testTelegramConnection(); }
    testDiscordConnection() { return this.controller.settingsManager.testDiscordConnection(); }
    testFirecrawlConnection() { return this.controller.settingsManager.testFirecrawlConnection(); }
    clearTelegramSettings() { return this.controller.settingsManager.clearTelegramSettings(); }
    clearDiscordSettings() { return this.controller.settingsManager.clearDiscordSettings(); }
    clearFirecrawlSettings() { return this.controller.settingsManager.clearFirecrawlSettings(); }
    handleTelegramAutoSendToggle(enabled) { return this.controller.settingsManager.handleTelegramAutoSendToggle(enabled); }
    handleDiscordAutoSendToggle(enabled) { return this.controller.settingsManager.handleDiscordAutoSendToggle(enabled); }

    // Event handling methods
    setupEventListeners() { return this.controller.eventManager.setupEventListeners(); }
    setupMessageListeners() { return this.controller.eventManager.setupMessageListeners(); }
    setupEventDelegation() { return this.controller.eventManager.setupEventDelegation(); }
    checkPendingAnalysis() { return this.controller.eventManager.checkPendingAnalysis(); }
    checkContextMenuActions() { return this.controller.eventManager.checkContextMenuActions(); }

    // Additional prompt methods
    displayPromptList(prompts, pagination) { return this.controller.promptUIManager.displayPromptList(prompts, pagination); }
    loadTagFilters() { return this.controller.promptUIManager.loadTagFilters(); }
    filterByTag(tag) { return this.controller.promptUIManager.filterByTag(tag); }
    usePrompt(id) { return this.controller.promptUIManager.usePrompt(id); }
    togglePromptPin(id) { return this.controller.promptUIManager.togglePromptPin(id); }
    editPrompt(id) { return this.controller.promptUIManager.editPrompt(id); }
    deletePrompt(id) { return this.controller.promptUIManager.deletePrompt(id); }
    deletePromptConfirm(id) { return this.controller.promptUIManager.deletePromptConfirm(id); }
    copyPromptContent(id) { return this.controller.promptUIManager.copyPromptContent(id); }

    // Key management methods
    handleNewKeyValidation() { return this.controller.settingsManager.handleNewKeyValidation(); }
    updateNewKeyStatus(status, message) { return this.controller.settingsManager.updateNewKeyStatus(status, message); }
    setupKeyManagementEventListeners() { return this.controller.settingsManager.setupKeyManagementEventListeners(); }
    setupTelegramEventListeners() { return this.controller.settingsManager.setupTelegramEventListeners(); }
    setupDiscordEventListeners() { return this.controller.settingsManager.setupDiscordEventListeners(); }
    editTelegramSettings() { return this.controller.settingsManager.editTelegramSettings(); }
    editDiscordSettings() { return this.controller.settingsManager.editDiscordSettings(); }

    // UI helper methods
    showHelp() { return this.controller.uiManager.showHelp(); }
    showAbout() { return this.controller.uiManager.showAbout(); }
    closePromptEditor() { return this.controller.uiManager.closePromptEditor(); }
    closeKeyManagementModal() { return this.controller.uiManager.closeKeyManagementModal(); }
    showKeyManagementModal() { return this.controller.uiManager.showKeyManagementModal(); }
    escapeHtml(text) { return this.controller.uiManager.escapeHtml(text); }
}

// Initialize the analyzer when the popup loads
document.addEventListener('DOMContentLoaded', () => {
    window.analyzer = new AgentHustleAnalyzer();
});
