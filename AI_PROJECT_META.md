# <PERSON><PERSON>lePlug Chrome Extension - AI Project Rules

**Project Name:** <PERSON><PERSON><PERSON>P<PERSON> (Agent Hu<PERSON>le Pro Analyzer)  
**Project Type:** Chrome Extension (Manifest V3)  
**Primary Languages:** JavaScript (ES6+), HTML, CSS  
**Framework/Stack:** Vanilla JS with modular architecture, Chrome Extension APIs  
**Deployment Target:** Chrome Web Store + Render.com API backend  

---

## 1. Project Overview and Architecture

### Core Purpose
AI-powered analysis Chrome extension that analyzes selected text, full pages, and URLs using Agent Hustle's API platform. Features Pro membership system with advanced integrations (Telegram, Discord, Firecrawl).

### Architecture Pattern
- **Modular Manager-Based Architecture**: PopupController orchestrates specialized managers
- **Manager Pattern**: BaseManager → Specialized managers (UIManager, AnalysisManager, etc.)
- **Storage Strategy**: Chrome sync storage for settings, local storage for cache
- **API Integration**: Background service worker handles all external API calls

### Key Components
```
js/
├── popup/
│   ├── core/           # PopupController, BaseManager
│   ├── ui/             # UIManager, EventManager  
│   ├── analysis/       # AnalysisManager
│   ├── data/           # DataManager
│   ├── settings/       # SettingsManager
│   ├── integrations/   # IntegrationManager, AutoSendManager
│   └── prompts/        # PromptUIManager
├── auth/               # Pro validation, permanent status
├── integrations/       # Discord, Telegram, Firecrawl
├── security/           # Hash utilities
├── user/               # User settings, Pro status
└── utils/              # Pagination, membership checking
```

---

## 2. File Organization and Size Limits

### Structure Rules
- **Maximum file size**: 500 lines (current files exceed this - refactor when editing)
- **Manager pattern**: All popup functionality through specialized managers
- **Import organization**: ES6 modules with explicit imports
- **Configuration centralization**: All config in `config.js`

### Critical Files
- `popup.js` - Main entry point, initializes AgentHustleAnalyzer
- `background.js` - Service worker, handles API calls and context menus
- `manifest.json` - Extension configuration (Manifest V3)
- `config.js` - All endpoints, timeouts, and feature flags

---

## 3. Language and Coding Standards

### JavaScript Standards
- **ES6+ modules** with explicit imports/exports
- **ESLint configuration**: `eslint.config.js` with SonarJS plugin
- **Async/await** preferred over Promises
- **Error handling**: Try-catch with specific error messages
- **Console logging**: Structured with emojis (🚀, ❌, ✅, 📊, etc.)

### Naming Conventions
- **Classes**: PascalCase (PopupController, BaseManager)
- **Methods**: camelCase (performAnalysis, showSection)
- **Constants**: UPPER_SNAKE_CASE (API_ENDPOINT, PRO_VALIDATION_ENDPOINT)
- **Storage keys**: camelCase with prefixes (hustleProKey, permanentProStatus_)

---

## 4. Security and Secrets Management

### API Key Management
- **Storage**: Chrome sync storage (`agentHustleApiKey`, `hustleProKey`)
- **Transmission**: X-API-Key header for Agent Hustle API
- **Hashing**: SHA-256 with salt `AgentHustle2024ProSalt!@#$%^&*()`
- **Never commit**: Raw API keys, Pro keys, or database credentials

### Pro Key Security
- **Permanent storage**: `permanentProStatus_${keyHash.substring(0, 16)}`
- **Validation endpoint**: `https://plugin-api-4m2r.onrender.com/api`
- **Rate limiting**: 3 requests per 10 seconds per IP
- **Fallback validation**: GitHub Pages JSON for offline validation

### Environment Variables (Render.com)
```
TURSO_DATABASE_URL=<turso-db-url>
TURSO_AUTH_TOKEN=<turso-token>
PRO_SALT=AgentHustle2024ProSalt!@#$%^&*()_+SecureKey
```

---

## 5. Testing Requirements

### Test Framework
- **Jest** with jsdom environment (`tests/jest.config.js`)
- **Coverage target**: 80% branches, functions, lines, statements
- **Test organization**: Unit, integration, performance, UI tests

### Test Commands
```bash
# Run all tests
npm test

# Run specific test suite
npx jest permanentProStatus.test.js

# Generate coverage
npx jest --coverage

# Run performance benchmarks
node tests/run-tests.js
```

### Performance Targets
- **Permanent status check**: <100ms
- **Complete validation**: <100ms  
- **Storage operations**: <200ms
- **Migration operations**: <500ms

---

## 6. Development Workflow

### Local Development
```bash
# Load extension in Chrome
1. Open chrome://extensions/
2. Enable Developer mode
3. Load unpacked: select project folder

# Test API endpoints
node vercel-api/test-api.js

# Run validation tests
node tests/validate-implementation.js
```

### Required Tools
- **Chrome Browser** (latest version)
- **Node.js** (for API backend and testing)
- **Jest** (testing framework)
- **ESLint** (code quality)

---

## 7. Git and Version Control

### Branching Model
- **main**: Production-ready code
- **feature branches**: New features and fixes
- **No direct commits** to main branch

### Commit Message Style
- Use descriptive prefixes: `feat:`, `fix:`, `refactor:`, `test:`
- Reference PRPs when applicable: `feat: implement permanent Pro status (PRP-001)`

---

## 8. Documentation Standards

### Code Documentation
- **JSDoc comments** for public methods
- **Inline comments** for complex logic
- **README updates** for new features
- **PRP documentation** in `PRPs/` folder for major changes

### File Headers
```javascript
/**
 * [Manager Name]
 * [Brief description of functionality]
 */
```

---

## 9. Performance Guidelines

### Chrome Extension Optimization
- **Minimize background script** activity
- **Use chrome.storage efficiently** (batch operations)
- **Implement proper caching** for Pro validation
- **Lazy load** non-critical managers

### API Performance
- **Permanent Pro status**: Eliminate repeated API calls after first validation
- **Rate limiting**: Respect API cooldowns (5000ms for Pro validation)
- **Timeout handling**: 30s for Firecrawl, 10s for integrations

---

## 10. Deployment and Release Process

### Chrome Extension
1. Update `manifest.json` version
2. Test in development mode
3. Package extension: `package-extension.ps1`
4. Submit to Chrome Web Store

### API Backend (Render.com)
1. Push to main branch (auto-deploy enabled)
2. Monitor health endpoint: `/health`
3. Check database connectivity
4. Verify environment variables

---

## 11. Code Review Checklist

### Manager Integration
- ✅ Extends BaseManager properly
- ✅ Registered with PopupController
- ✅ Proper initialization order
- ✅ Error handling through handleError()

### Chrome Extension Compliance
- ✅ Manifest V3 compatibility
- ✅ Proper permission usage
- ✅ Service worker best practices
- ✅ Content script isolation

### Security Review
- ✅ No hardcoded secrets
- ✅ Proper input validation
- ✅ Secure storage patterns
- ✅ HTTPS-only API calls

---

## 12. Critical Violations Summary

### 🚫 Never Do
- **Delete existing managers** without migration plan
- **Bypass PopupController** for manager communication
- **Hardcode API keys** or Pro keys in source
- **Use synchronous storage** operations
- **Ignore error handling** in async operations
- **Break Manifest V3** compliance
- **Commit sensitive data** (keys, tokens, credentials)

### 🚫 Architecture Violations
- **Direct DOM manipulation** outside UIManager
- **Cross-manager dependencies** without controller
- **Blocking operations** in background script
- **Unhandled promise rejections**

---

## 13. Pattern Awareness

### 🔍 Always Check First
Before implementing new features, search for existing patterns:
- **Manager implementations**: Check other managers in `js/popup/`
- **Storage patterns**: Look for similar Chrome storage usage
- **API integrations**: Review existing integration patterns
- **Error handling**: Follow established error patterns

### Reuse Utilities
- **BaseManager**: For all popup managers
- **hashUtils.js**: For key hashing operations
- **pagination.js**: For list pagination
- **membershipChecker.js**: For Pro status validation

---

## 14. Integration Patterns

### Pro Features
- **Check Pro status** before enabling features
- **Graceful degradation** for non-Pro users
- **Expiration warnings** for approaching expiry
- **Auto-send integrations** only for Pro users

### Chrome Storage Keys
```javascript
// Sync storage (cross-device)
agentHustleApiKey, hustleProKey, hustleProStatus
telegramSettings, discordSettings, firecrawlSettings

// Local storage (device-specific)  
proStatus_${hash}, analysisHistory, promptLibrary
```

---

**Last Updated**: 2025-07-11  
**Review Schedule**: Monthly or before major releases  
**Maintainer**: HustlePlug Development Team
